version: 2.1
orbs:
  node: circleci/node@7

jobs:
  build-and-test:
    executor: node/default
    steps:
      - checkout
      - node/install-packages
      - run:
          name: Setup test results directory
          command: mkdir -p test-results/jest
      - run:
          name: Run tests with JUnit reporter
          command: |
            JEST_JUNIT_OUTPUT_DIR=test-results/jest JEST_JUNIT_OUTPUT_NAME=results.xml npm test -- --reporters=default --reporters=jest-junit
      - store_test_results:
          path: test-results
      - store_artifacts:
          path: test-results
          destination: test-results

workflows:
  version: 2
  build-test:
    jobs:
      - build-and-test
